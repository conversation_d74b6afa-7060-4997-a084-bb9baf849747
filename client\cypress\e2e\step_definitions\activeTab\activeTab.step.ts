import { DataTable, Given, Then, When } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';
import { ButtonActionMap, ButtonActions } from '../../../support/helperFunction/activeTabHelper';

Given(
  /^The user deletes the following Events if exist:(?:\s*"([^"]+)")?$/,
  function (
    eventName: string | undefined,
    eventNamesDataTable: DataTable | undefined
  ) {
    mainPage.deleteEventsIfExist(
      eventName,
      eventNamesDataTable
    );
  }
);

Given(
  /^The user deletes the following Files if exist:(?:\s*"([^"]+)")?$/,
  function (
    eventName: string | undefined,
    eventNamesDataTable: DataTable | undefined
  ) {
    mainPage.deleteFilesIfExist(
      eventName,
      eventNamesDataTable
    );
  }
);

Given('The user is on Event Screen', () => {
  mainPage.visit();
});

When('The user clicks on {string}', (buttonText: string) => {
  const buttonActionMap: ButtonActionMap = {
    [ButtonActions.VIEW_EVENT]: () => mainPage.clickViewEventButton(),
    [ButtonActions.VIEW_FILE]: () => mainPage.clickViewFileButton(),
    [ButtonActions.DELETE_EVENT]: () => mainPage.clickDeleteEventButton(),
    [ButtonActions.DELETE_FILE]: () => mainPage.clickDeleteFileButton(),
  };
  const action = buttonActionMap[buttonText as ButtonActions];
  action();
});

When('The user creates default event name {string}', (eventName: string) => {
  mainPage.clickUploadFilesButton();
  mainPage.clickNewEvent();
  mainPage.enterEventName(eventName);
  mainPage.clickCreateEvent();
});

When('The user selects a file to upload {string}', (fileName: string) => {
  cy.SelectFile({ fileName: fileName });
});

When('The user clicks upload files button', () => {
  mainPage.clickUploadFilesButton();
});

When('The user clicks upload button', () => {
  mainPage.clickUploadButton();
});

When('The user inputs {string} to search event name', (eventName: string) => {
  mainPage.enterSearchEventPopup(eventName);
});

When('The user clicks on the {string} tab', (tabName: string) => {
  mainPage.clickTab(tabName);
  if (tabName.toLowerCase() === 'files') {
    cy.waitFilesTabIsLoaded();
  } else {
    cy.waitMainPageIsLoaded();
  }
});

When('The user selects the {string} named {string}', (type: 'event' | 'file', eventName: string) => {
  mainPage.selectItem(eventName, type);
});

Then('The following {string} details should be visible:', (type: 'event' | 'file', dataTable: DataTable) => {
  const rows = dataTable.hashes() as unknown as Array<{ Field: string; 'Expected Value': string }>;
  mainPage.verifyDetails(rows, type);
});

Then('{string} Time has format: {string}', (type: 'event' | 'file', format: string) => {
  mainPage.verifyTimeFormat(format, type);
});

When('The user clicks the {string} name located on the right side', (type: 'event' | 'file') => {
  mainPage.clickDetailNameEdit(type);
});

When('The user changes {string} name to {string}', (type: 'event' | 'file', newName: string) => {
  mainPage.changeDetailName(newName, type);
});

Then('The name of the {string} should be updated to {string}', (type: 'event' | 'file', newName: string) => {
  mainPage.verifyDetailNameChanged(newName, type);
});

When('The user clicks on the {string} column header for {string}', (columnName: string, type: 'event' | 'file') => {
  mainPage.clickColumnHeader(columnName, type);
});

Then('The {string} table should be sorted by {string} in {string} order', (type: 'event' | 'file', columnName: string, sortedBy: 'a-z' | 'z-a') => {
  mainPage.clickColumnHeaderUntilSorted(columnName, sortedBy, type);
  mainPage.verifyColumnSortState(columnName, sortedBy, type);
});

Then('The user clicks on cancel upload button', () => {
  mainPage.clickCancelUpload();
});

Then('The user should see the {string} label', (label: string) => {
  if (label === 'Results Per Page') {
    mainPage.verifyResultsPerPageLabel();
  }
});

Then('The user should see a success snackbar with message {string}', (message: string) => {
  mainPage.verifySuccessSnackbar(message);
});

When('The user changes the results per page and verifies the following options:', (dataTable: DataTable) => {
  const rows = dataTable.hashes();
  rows.forEach((row) => {
    const perPage = row.PerPage;
    mainPage.changeResultsPerPage(perPage);
    mainPage.verifyResultsPerPageChanged(perPage);
  });
});

Then('The user should see the initial pagination state', () => {
  mainPage.verifyPaginationInitialState();
});

When('The user navigates to the {string} page', (direction: 'next' | 'previous') => {
  if (direction === 'next') {
    mainPage.clickNextPage();
  } else {
    mainPage.clickPreviousPage();
  }
});

Then('The pagination should update for the next page', () => {
  mainPage.verifyNavigatedToNextPage();
});

Then('The pagination should return to the initial state', () => {
  mainPage.verifyNavigatedToPreviousPage();
});

Then('The user verifies delete button is enabled and clicks it', () => {
  mainPage.verifyDeleteButtonEnabledAndClick();
});

Then('The user should navigate to file details page', () => {
  cy.url().should('include', '/file/');
});

When('The user enters event name {string}', (eventName: string) => {
  mainPage.enterEventNameToSearchInput(eventName);
});

Then('The user clicks the view file button in file detail', () => {
  mainPage.clickViewFileButtonInFileDetail();
});

Then('The view file button in file detail should be disabled', () => {
  mainPage.verifyViewFileButtonIsDisabled();
});

When('The user enters {string} into the search bar', (keyword: string) => {
  mainPage.enterSearchKeyword(keyword);
});

Then('The displayed {word} results should contain {string}', (searchType: string, keyword: string) => {
  if (searchType === 'event' || searchType === 'file') {
    const pluralSearchType = `${searchType}s` as 'events' | 'files';
    mainPage.verifySearchResults(keyword, pluralSearchType);
  } else {
    throw new Error(`Unsupported search type: ${searchType}. Expected 'event' or 'file'.`);
  }
});

Then('The user should navigate to event details page', () => {
  mainPage.verifyNavigationToEventDetails();
});
