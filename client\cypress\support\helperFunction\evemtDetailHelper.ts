export enum EventDetailDataTestId {
  HOME_DETAIL_NAME = 'home-detail-name',
  APPBAR_TITLE = 'appbarTitle',
  EVENT_BREADCRUMB = 'event-breadcrumb',
  EVENT_HEADING_NAME = 'event-heading-name',
  SEARCH_UPLOAD_UPLOAD_BUTTON = 'search-and-upload-upload-button',
  SEARCH_UPLOAD_SEARCH_ALL_FILE_BUTTON = 'search-and-upload-search-all-file-button',
  EVENT_FILES_TAB = 'event-files-tab',
  EVENT_MATCH_GROUPS_TAB = 'event-match-groups-tab',
  EVENT_TIMELINE_TAB = 'event-generated-timeline-videos-tab',
  EVENT_NO_RESULTS = 'event-tab-container-no-results',
  EVENT_EDIT_EVENT = 'event-edit-event',
  EVENT_CHANGE_NAME = 'event-change-name',
  EVENT_CHANGE_DESCRIPTION = 'event-change-description',
  EVENT_TAGS_AUTOCOMPLETE = 'event-tags-autocomplete-field-input',
  EVENT_SAVE_EVENT = 'event-save-event',
  EVENT_DESCRIPTION = 'event-description',
  EVENT_DATE = 'event-date',
  UPLOAD_FILE_BTN = 'upload-file-button',
  SNACKBAR_BOX = 'snackbar-box-1-1',
  EVENT_TABLES = 'event-tables',
  TABLE_PAGINATION_PAGE_SIZE = 'table-pagination-page-size',
  SEARCH_ALL_FILES_DIALOG_TITLE = 'search-all-files-dialog__title',
  SEARCH_ALL_FILES_DIALOG_ATTRIBUTE_ITEM = 'search-all-files-dialog__content-attribute-selection-category-menu-item',
  SEARCH_ALL_FILES_DIALOG_CLEAR_ALL = 'search-all-files-dialog__content-attribute-selected-clear-all',
  SEARCH_ALL_FILES_DIALOG_NEW_MATCH_GROUP = 'search-all-files-dialog__content-match-group-new-match-add',
  SEARCH_ALL_FILES_DIALOG_MATCH_GROUP_SELECT = 'search-all-files-dialog__content-match-group-select',
  SEARCH_ALL_FILES_DIALOG_CANCEL = 'search-all-files-dialog__actions-cancel',
  SEARCH_ALL_FILES_DIALOG_SEARCH = 'search-all-files-dialog__actions-search',
  MATCH_GROUP_ROW = 'match-group-row-',
  SEARCH_ALL_FILES_COUNT = 'search-all-files-dialog__content-attribute-selected-count',
  SEARCH_ALL_CHECKBOX_ITEM = 'search-all-files-dialog__content-attribute-selection-checkbox-menu-item',
  SEARCH_ALL_NEW_MATCH_INPUT = 'search-all-files-dialog__content-match-group-new-match-input',
  SEARCH_ALL_NEW_MATCH_CONFIRM = 'search-all-files-dialog__content-match-group-new-match-confirm',
  TABLE = 'table',
  COLUMN_TITLE = 'column-title',
  FILE_ROW_NAME = 'file-row-name',
  EVENT_DETAIL_CONTAINER = 'event-detail-container',
  EVENT_DETAIL_FILE_NAME = 'event-detail-file-name',
  CONFIRM_DIALOG_TITLE = 'confirm-dialog-title',
  CONFIRM_DIALOG_DESCRIPTION = 'confirm-dialog-description',
  CONFIRM_DIALOG_CANCEL_ACTION = 'confirm-dialog-cancel-action',
  CONFIRM_DIALOG_CONFIRM_ACTION = 'confirm-dialog-confirm-action',
}
