Feature: Event Details

  Background:
    Given The user is on Event Screen

  # @e2e @event-details
  # Scenario: Ensure a clean test event exists
  #   Given The user deletes the following Events if exist:
  #    | e2e-event-details-cypress         |
  #    | e2e-event-details-cypress-updated |
  #   When The user clicks upload files button
  #   And The user clicks on new event button
  #   Then The user enter "e2e-event-details-cypress" in the event name textbox
  #   And The user clicks on create event button
  #   Then The user should see a success snackbar with message "e2e-event-details-cypress"
  #   And The user clicks on cancel upload button
  #   Then The user should see "e2e-event-details-cypress" in the event table

  # @e2e @event-details
  # Scenario: Verify user can navigate to and see the UI of the Event Details page
  #   When The user selects the "event" named "e2e-event-details-cypress"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   And The user should see the header with application name "Track"
  #   And The user should see the breadcrumb containing the event name "e2e-event-details-cypress"
  #   And The user should see the event title "e2e-event-details-cypress"
  #   And The user should see the "Upload File" and "Search All Files in this Event" buttons
  #   And The user should see the tabs "Files", "Match Groups", and "Timeline Generated Videos"
  #   And The user should not see the table loading skeleton

  # @e2e @event-details
  # Scenario: Verify user can use breadcrumb to navigate back
  #   When The user selects the "event" named "e2e-event-details-cypress"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks "All Events" breadcrumb in Event Screen details
  #   And The user should see the correct column headers for Event Name and Event Time
  #   Then The user should see the application title Track

  # @e2e @event-details @fail
  # Scenario: Verify user can edit event details (name, description, tags, and datetime)
  #   When The user selects the "event" named "e2e-event-details-cypress"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the edit event button
  #   And The user enters "e2e-event-details-cypress-updated" into the event name field
  #   And The user enters "This is an updated description." into the description field
  #   And The user adds the tag "important-tag"
  #   And The user sets the start date to "20" of "May"
  #   And The user clicks the save event button
  #   And The user should see the notification message "Updated event successfully"
  #   Then The event title should be updated to "e2e-event-details-cypress-updated"
  #   And The event description should be updated to "This is an updated description."
  #   And The event tags should include "important-tag"
  #   And The event date range should start with "5/20/2025"
  #   Given The user is on Event Screen
  #   When The user selects the "event" named "e2e-event-details-cypress-updated"
  #   Then The user should see "e2e-event-details-cypress-updated" in the event detail panel

  # @e2e @event-details @fail
  # Scenario: Verify a very long description does not break the layout
  #   When The user selects the "event" named "e2e-event-details-cypress"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the edit event button
  #   And The user enters the long description "This is a very long string designed to test word wrapping and layout. It should not overflow its container but instead wrap to multiple lines, proving the CSS is handling overflow correctly. This is a very long string designed to test word wrapping and layout."
  #   And The user clicks the save event button
  #   Then The long description is displayed correctly and wraps within its container
  #   Given The user is on Event Screen
  #   When The user selects the "event" named "e2e-event-details-cypress"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   Then The long description is displayed correctly and wraps within its container

  # @e2e @event-details
  # Scenario: Verify user can change the number of results per page
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   Then The user changes and verifies the results per page for the following values:
  #     | 100 |
  #     |  50 |
  #     |  10 |

  # @e2e @event-details
  # Scenario: Verify user can navigate between pages using pagination controls
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   Then The "next" page button should be "enabled"
  #   And The "previous" page button should be "disabled"
  #   When The user clicks the "next" page button
  #   Then The "previous" page button should be "enabled"
  #   And The "next" page button should be "disabled"
  #   When The user clicks the "previous" page button
  #   Then The "next" page button should be "enabled"
  #   And The "previous" page button should be "disabled"

  # @e2e @event-details @search-files
  # Scenario: Verify the UI of the Search All Files dialog
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   Then The Search All Files dialog should open and display a list of attributes
  #   And The dialog should display the following elements:
  #     | element description    |
  #     | People radio button    |
  #     | Vehicles radio button  |
  #     | Clear All button       |
  #     | New Match Group button |
  #     | Match Groups select    |
  #     | Cancel button          |
  #     | Search People button   |

  # @e2e @event-details @search-files
  # Scenario: Verify the correct attributes are listed for People and Vehicles in the search dialog
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   Then The dialog should display the following attributes for "People":
  #     | Accessory |
  #     | Body      |
  #     | Face      |
  #     | Footwear  |
  #     | Gender    |
  #     | Hair      |
  #     | Lower     |
  #     | Upper     |
  #   When The user selects the "Vehicles" option in the search dialog
  #   Then The dialog should display the following attributes for "Vehicles":
  #     | Cosmetic-Damage |
  #     | Decade          |
  #     | Door-Count      |
  #     | Driver          |
  #     | License-Plate   |
  #     | Make            |
  #     | Model           |
  #     | Passenger       |
  #     | Type            |
  #     | Vehicle-Color   |
  #     | Wheel-Color     |
  #     | Wheel-Style     |
  #     | Window-Tint     |

  # @e2e @event-details @search-files
  # Scenario: Verify user can select attributes and see the count update
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   And The attribute selected count should show 0
  #   When The user selects the attribute category "Body"
  #   And The user "checks" the box for the attribute "Overweight"
  #   Then The attribute selected count should show 1
  #   When The user "checks" the box for the attribute "Average"
  #   Then The attribute selected count should show 2
  #   When The user "un-checks" the box for the attribute "Overweight"
  #   Then The attribute selected count should show 1

  # @e2e @event-details @search-files
  # Scenario: Verify user cannot search without selecting an attribute
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   And The user clicks the "Search People" button in the search dialog
  #   Then The user should see the notification message "Please select an attribute to search"

  # @e2e @event-details @search-files
  # Scenario: Verify user can clear all selected attributes
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   And The user selects the attribute category "Body"
  #   And The user "checks" the box for the attribute "Overweight"
  #   And The user "checks" the box for the attribute "Average"
  #   Then The attribute selected count should show 2
  #   When The user clicks the "Clear All" button in the search dialog
  #   Then The attribute selected count should show 0

  # @e2e @event-details @search-files @match-group
  # Scenario: Verify searching with a new Match Group and managing its lifecycle
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   And The user selects the attribute category "Body"
  #   And The user "checks" the box for the attribute "Overweight"
  #   And The user clicks the "New Match Group" button in the search dialog
  #   And The user creates a new match group named "E2E-Match-Group"
  #   And The user clicks the "Search People" button in the search dialog
  #   Then The user should see the notification message "Match Group E2E-Match-Group was created successfully"
  #   And The user "checks" the box for the attribute "Average"
  #   And The user clicks the "Search People" button in the search dialog
  #   Then The user should see the notification message "Match Group E2E-Match-Group was updated successfully"
  #   When The user clicks the "Match Groups" tab
  #   Then The match group "E2E-Match-Group" should be listed with "1" searches
  #   When The user deletes the match group "E2E-Match-Group"
  #   And The user clicks the "Yes, Delete" button in the confirmation dialog
  #   Then The user should see the notification message "MatchGroup successfully deleted"
  #   And The match group "E2E-Match-Group" should no longer be listed

  # @e2e @event-details @search-files
  # Scenario: Verify user can cancel an attribute search
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks the "Search All Files in this Event" button
  #   Then The Search All Files dialog should open and display a list of attributes
  #   When The user selects the attribute category "Body"
  #   And The user "checks" the box for the attribute "Overweight"
  #   Then The attribute selected count should show 1
  #   When The user clicks the "Cancel" button in the search dialog
  #   Then The search dialog should be closed
  #   When The user clicks the "Search All Files in this Event" button
  #   Then The Search All Files dialog should open and display a list of attributes
  #   And The attribute selected count should show 0

  # @e2e @event-details @files-tab
  # Scenario: Verify Files tab displays correctly and shows file details on selection
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   And The user should see the following columns in the Files tab:
  #     | File Name   |
  #     | File Status |
  #     | Upload Date |
  #   When The user clicks on the file named "lucy.mp4"
  #   Then The file preview and information for "lucy.mp4" should be displayed

  # @e2e @event-details @files-tab
  # Scenario: Verify Files tab when the event has no files
  #   When The user enters "Cypress E2E Test" into the search bar
  #   Then The displayed event results should contain "Cypress E2E Test"
  #   When The user selects the "event" named "Cypress E2E Test"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   And The user should see the "No Files Found" message for the event "Cypress E2E Test"

  # @e2e @event-details @files-tab
  # Scenario: Verify the format of Upload Date
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   And The Upload Date for each file should be in the correct format

  # @e2e @event-details @files-tab
  # Scenario: Verify user can View file
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks on the file named "lucy.mp4"
  #   And The user clicks the "View File" action button
  #   Then The user should be navigated to the file details page

  # @e2e @event-details @files-tab
  # Scenario: Verify the delete file confirmation dialog
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks on the file named "lucy.mp4"
  #   And The user clicks the "Delete File" action button
  #   Then The delete confirmation dialog for "lucy.mp4" should be displayed
  #   And The "Delete" button in the confirmation dialog should be enabled
  #   When The user clicks the "Cancel" button in the confirmation dialog
  #   Then The file "lucy.mp4" should still be in the list

  # @e2e @event-details @files-tab
  # Scenario: Verify user can play video preview
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks on the file named "lucy.mp4"
  #   Then The media player should be visible
  #   Then The user clicks the "play" control and verifies the video starts playing

  # @e2e @event-details @files-tab @video-player
  # Scenario: Verify seeking and jumping in video preview
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks on the file named "lucy.mp4"
  #   Then The user clicks the "play" control and verifies the video starts playing
  #   And The user clicks the "jump forward" control and verifies the time increases
  #   And The user clicks the "jump back" control and verifies the time decreases

  # @e2e @event-details @files-tab
  # Scenario Outline: Verify user can sort files by <ColumnName> <SortedBy>
  #   When The user enters "lucy" into the search bar
  #   Then The displayed event results should contain "lucy"
  #   When The user selects the "event" named "lucy"
  #   And The user clicks on "View Event"
  #   Then The user should navigate to event details page
  #   When The user clicks on the "<ColumnName>" column header for "file"
  #   Then The "file" table should be sorted by "<ColumnName>" in "<SortedBy>" order

  #   Examples:
  #     | ColumnName  | SortedBy |
  #     | File Name   | z-a      |
  #     | File Name   | a-z      |
  #     | Upload Date | z-a      |
  #     | Upload Date | a-z      |

  @e2e @event-details @files-tab
  Scenario: Verify that the status of files in the Event Details matches that in the Files tab on the Homepage
    When The user enters "lucy" into the search bar
    Then The displayed event results should contain "lucy"
    When The user selects the "event" named "lucy"
    And The user clicks on "View Event"
    Then The user should navigate to event details page
    When The user clicks "All Events" breadcrumb in Event Screen details
    Then The user should see the correct column headers for Event Name and Event Time
    When The user clicks on the "Files" tab
    Then The file status should match between Files tab and Event Details for the following files:
      | fileName          | expectedStatus |
      | lucy.mp4          | PROCESSED      |
      | lucy1.mp4         | ERROR          |
      | swahili-mp4.mp4   | PROCESSED      |
      | spanish-txt.txt   | ERROR          |